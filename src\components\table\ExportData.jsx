import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  addToast,
} from "@heroui/react";
import { useDisclosure } from "@heroui/react";
import PropTypes from "prop-types";
import { useForm } from "react-hook-form";
import * as XLSX from "xlsx";
import FormGroupCheckbox from "../form/FormGroupCheckbox";

/**
 * @param {ExportDataProps} props
 */
const ExportData = ({
  data,
  columns,
  queryKey,
  selectedRowKeys,
  buttonProps,
  textButton = "دریافت فایل اکسل",
}) => {
  const { control: exportControl, getValues: getExportValues } = useForm({
    defaultValues: {
      export: columns
        .filter((item) => item.exportable !== false)
        .map((item) => `${item.id}`),
    },
  });

  const {
    isOpen: isExportOpen,
    onOpen: onExportOpen,
    onOpenChange: onExportOpenChange,
  } = useDisclosure();

  const { isLoading: isFullExportLoading, refetch: fetchFullExportData } =
    queryKey.useQuery({
      variables: {
        query: {
          _excel: true,
        },
      },
      enabled: false,
    });

  const handleExport = async () => {
    const selectedColumnIds = getExportValues("export");

    let dataToExport;

    if (selectedRowKeys === "all") {
      const { data: fetchedData, error: fetchError } =
        await fetchFullExportData();

      if (fetchError || !fetchedData?.data?.data) {
        addToast({
          title: fetchError?.message
            ? fetchError.message
            : "مشکلی در دریافت اطلاعات کامل پیش آمد",
          color: "danger",
        });
        onExportOpenChange(false);
        return;
      }
      dataToExport = fetchedData?.data?.data;
    } else {
      const selectedRows = data?.filter((item) =>
        selectedRowKeys.has(`${item.id}`),
      );
      dataToExport = selectedRows;
    }

    const filteredColumns = columns.filter((column) =>
      selectedColumnIds.includes(column.id),
    );

    const exportData = dataToExport.map((row) => {
      const rowData = {};
      for (const col of filteredColumns) {
        let value = row[col.field];
        if (typeof col.field === "function") {
          value = col.field(row);
          if (col.type === "user") {
            value = value.name;
          }
        } else if (col.type === "status") {
          value = value ? "فعال" : "غیرفعال";
        }
        rowData[col.title] = value;
      }
      return rowData;
    });

    const worksheet = XLSX.utils.json_to_sheet(exportData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Students");
    XLSX.writeFile(workbook, "students.xlsx");
    onExportOpenChange(false);
  };

  return (
    <>
      <Button
        onPress={onExportOpen}
        color="secondary"
        variant="bordered"
        radius="full"
        isDisabled={(data && !selectedRowKeys) || selectedRowKeys.size === 0}
        isLoading={isFullExportLoading}
        {...buttonProps}
      >
        {textButton}
      </Button>

      <Modal
        size="lg"
        isOpen={isExportOpen}
        placement="center"
        className="mx-4"
        onOpenChange={onExportOpenChange}
      >
        <ModalContent className="rounded-small px-2 pb-5 pt-3 sm:px-4 md:px-6 md:pb-6 md:pt-4 lg:px-8 lg:pb-8">
          {() => (
            <>
              <ModalHeader className="flex flex-col gap-1" />
              <ModalBody className="gap flex flex-col items-center">
                <p className="text-lg font-semibold">انتخاب موارد خروجی اکسل</p>

                <FormGroupCheckbox
                  name="export"
                  control={exportControl}
                  color="secondary"
                  items={columns
                    .filter((item) => item.exportable !== false)
                    .map((item) => ({
                      label: item.title,
                      value: `${item.id}`,
                    }))}
                />
              </ModalBody>
              <ModalFooter>
                <Button
                  color="secondary"
                  radius="full"
                  fullWidth
                  size="lg"
                  className="text-base font-medium"
                  onPress={handleExport}
                >
                  دریافت خروجی اکسل
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
};

ExportData.propTypes = {
  data: PropTypes.array.isRequired,
  columns: PropTypes.array.isRequired,
  queryKey: PropTypes.string.isRequired,
  buttonProps: PropTypes.object,
  selectedRowKeys: PropTypes.oneOfType([PropTypes.string, PropTypes.object])
    .isRequired,
  textButton: PropTypes.string,
};

export default ExportData;
