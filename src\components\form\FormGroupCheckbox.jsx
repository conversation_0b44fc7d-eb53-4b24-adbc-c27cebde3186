import { Checkbox, CheckboxGroup, cn } from "@heroui/react";
import PropTypes from "prop-types";
import { useEffect } from "react";
import { useController } from "react-hook-form";

/**
 * @param {FormGroupCheckboxProps} props
 */
const FormGroupCheckbox = (props) => {
  const {
    name,
    control,
    className,
    checkboxClassName,
    items,
    size,
    defaultValue = [],
    ...other
  } = props;

  const {
    field: { onChange, value, ...field },
    fieldState,
  } = useController({ name, control });

  const defaultProps = {
    className: cn("w-full z-0", className),
    ...other,
  };

  useEffect(() => {
    onChange(props?.defaultValue);
  }, [props?.defaultValue]);

  return (
    <CheckboxGroup
      {...field}
      value={value}
      isInvalid={!!fieldState.error}
      onValueChange={(data) => {
        onChange(data);
      }}
      {...defaultProps}
      description={fieldState.error?.message}
    >
      {items?.map((item) => (
        <Checkbox
          key={item.value}
          size={size}
          value={item.value}
          className={cn("", checkboxClassName)}
        >
          {item.label}
        </Checkbox>
      ))}
    </CheckboxGroup>
  );
};

FormGroupCheckbox.propTypes = {
  name: PropTypes.string.isRequired,
  control: PropTypes.object.isRequired,
  color: PropTypes.string,
  size: PropTypes.oneOf(["sm", "md", "lg"]),
  radius: PropTypes.oneOf([
    "none",
    "base",
    "xs",
    "sm",
    "md",
    "lg",
    "xl",
    "full",
  ]),
  className: PropTypes.string,
  label: PropTypes.string,
  defaultValue: PropTypes.arrayOf(PropTypes.string),
  items: PropTypes.arrayOf(PropTypes.object),
  orientation: PropTypes.oneOf(["horizontal", "vertical"]),
  checkboxClassName: PropTypes.string,
};

export default FormGroupCheckbox;
