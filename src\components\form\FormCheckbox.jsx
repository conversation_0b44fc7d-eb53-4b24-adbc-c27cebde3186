import { Checkbox, cn } from "@heroui/react";
import PropTypes from "prop-types";
import { use<PERSON>ontroller } from "react-hook-form";

/**
 * @param {FormCheckboxProps} props
 */
const FormCheckbox = (props) => {
  const { name, control, className, wrapperClassName, label, ...other } = props;

  const {
    field: { onChange, value, ...field },
    fieldState,
  } = useController({ name, control });

  const defaultProps = {
    className: cn("w-full z-0", className),
    ...other,
  };

  return (
    <div className={cn("flex flex-col gap-2", wrapperClassName)}>
      <Checkbox
        {...field}
        isInvalid={!!fieldState.error}
        onValueChange={onChange}
        isSelected={value}
        {...defaultProps}
      >
        {label}
      </Checkbox>
      {fieldState.error && (
        <p className="text-sm text-red-500">{fieldState.error.message}</p>
      )}
    </div>
  );
};

FormCheckbox.propTypes = {
  name: PropTypes.string.isRequired,
  control: PropTypes.object.isRequired,
  className: PropTypes.string,
  radius: PropTypes.oneOf(["none", "sm", "md", "lg", "full"]),
  size: PropTypes.oneOf(["sm", "md", "lg"]),
  label: PropTypes.string,
  color: PropTypes.string,
  wrapperClassName: PropTypes.string,
};

export default FormCheckbox;
