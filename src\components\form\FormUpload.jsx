import { Button, Input, cn } from "@heroui/react";
import { Apple, CloseCircle, VideoAdd } from "iconsax-reactjs";
import PropTypes from "prop-types";
import { useCallback, useRef } from "react";
import { useDropzone } from "react-dropzone";
import { useController } from "react-hook-form";
import ReactPlayer from "react-player";

const FormUpload = (props) => {
  const { control, name, isMultiple, classNames, accept } = props;

  const fileInputRef = useRef(null);

  const {
    field: { onChange, value },
    fieldState,
  } = useController({
    name,
    control,
    defaultValue: [],
  });

  const onDrop = useCallback(
    (acceptedFiles) => {
      const newFiles = acceptedFiles.filter(
        (file) => !value.some((f) => f.name === file.name),
      );
      onChange(isMultiple ? [...value, ...newFiles] : newFiles);
    },
    [value, isMultiple, onChange],
  );

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: getAcceptedFormats(accept),
    noClick: true,
  });

  const removeFile = (file) => {
    onChange(value.filter((f) => f !== file));
    if (fileInputRef?.current) {
      fileInputRef.current.value = "";
    }
  };

  const removeAll = () => onChange([]);

  const { onDragEnter, onDragLeave, onDrop: onDropProp } = getRootProps();

  return (
    <div
      onDragEnter={onDragEnter}
      onDragLeave={onDragLeave}
      onDrop={onDropProp}
      className="flex flex-col items-center justify-center shadow-sm gap-3 w-full rounded-3xl h-60 border border-foreground-100 bg-background p-4"
    >
      <Input
        {...getInputProps()}
        ref={fileInputRef}
        className="hidden"
        onValueChange={onChange}
      />

      <RenderContent
        value={value}
        isMultiple={isMultiple}
        error={fieldState?.error}
        className={classNames?.uploadIcon}
        fileInputRef={fileInputRef}
        removeFile={removeFile}
      />

      {/* 
      {value.length > 0 &&
        !isMultiple &&
        value.map((file) => {
          return (
            <Button
              isIconOnly
              onPressEnd={() => removeFile(file)}
              key={file.path}
              className={cn(
                "absolute right-4 top-4 block h-6 min-h-1 w-6 min-w-1 rounded-full",
                classNames?.removeButton,
              )}
            >
              x
            </Button>
          );
        })}

      {value.length > 0 && isMultiple && (
        <div className="flex w-full flex-col items-start gap-3">
          {FileList({
            files: value,
            accept,
            removeFile,
            classNames: classNames?.fileList,
          })}

          <Button
            onPressEnd={removeAll}
            className={cn("h-10 min-h-3", classNames?.removeAllButton)}
          >
            پاک کردن همه
          </Button>
        </div>
      )} */}
    </div>
  );
};

const RenderContent = ({
  value,
  isMultiple,
  error,
  className,
  fileInputRef,
  removeFile,
  removeAll,
}) => {
  if (value.length === 0) {
    return (
      <>
        <Button
          className="px-8 "
          color="primary"
          radius="full"
          onPress={() => {
            fileInputRef.current?.click();
          }}
          startContent={<VideoAdd className="size-5" />}
        >
          ویدیو را انتتخاب کنید
        </Button>

        <p className="text-foreground-300">یا اینجا رها کنید</p>
        {error && <p className="text-sm text-red-500">{error.message}</p>}
      </>
    );
  }
  const path = URL.createObjectURL(value[0]);
  return (
    <>
      {value[0].type.startsWith("image/") ? (
        <img
          className="size-full object-cover"
          alt={value[0].name}
          src={path}
        />
      ) : value[0].type.startsWith("video/") ? (
        <div className="max-h-full aspect-video flex relative group ">
          <ReactPlayer
            url={path}
            controls={true}
            fallback={
              <div className="absolute bg-foreground-100 inset-0 flex items-center justify-center">
                درحال بارگزاری...
              </div>
            }
            height="100%"
            width="100%"
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              // aspectRatio: "16/9",
            }}
            config={{
              file: {
                attributes: {
                  style: {
                    height: "100%",
                    objectFit: "contain",
                    aspectRatio: "16/9",
                  },
                },
              },
            }}
          />

          <div className="flex group-hover:opacity-100 opacity-50 transition-opacity items-center absolute top-3 end-3 gap-2">
            <Button
              onPress={() => {
                fileInputRef.current?.click();
              }}
              size="sm"
              color="primary"
              variant="solid"
              radius="full"
            >
              انتخاب مجدد
            </Button>
            <Button
              isIconOnly
              size="sm"
              radius="full"
              className="size-8 min-h-0 min-w-0 "
              variant="solid"
              color="danger"
              onPress={() => {
                removeFile(value[0]);
              }}
            >
              <CloseCircle className="size-5" />
            </Button>
          </div>
        </div>
      ) : (
        <>
          <Apple className={cn("size-1/2", className)} />
          <p>{value[0].name}</p>
        </>
      )}
    </>
  );
};

RenderContent.propTypes = {
  value: PropTypes.arrayOf(PropTypes.object).isRequired,
  isMultiple: PropTypes.bool,
  accept: PropTypes.arrayOf(PropTypes.string),
  error: PropTypes.object.isRequired,
  className: PropTypes.string,
  fileInputRef: PropTypes.object.isRequired,
  removeFile: PropTypes.func.isRequired,
  removeAll: PropTypes.func.isRequired,
};

const FileList = ({ files, accept, removeFile, classNames }) => {
  return (
    <div className="flex w-full flex-wrap gap-3">
      {files.map((file) => {
        const path = URL.createObjectURL(file);
        return accept?.includes("image") ? (
          <span
            className={cn(
              "group relative flex size-20 items-center justify-between overflow-hidden rounded-md border border-foreground-200 p-1",
              classNames?.imageItem,
            )}
            key={file.path}
          >
            <img className="aspect-square size-full object-cover" src={path} />
            <Button
              isIconOnly
              onPressEnd={() => removeFile(file)}
              key={file.path}
              className={cn(
                "absolute right-1 top-1 h-4 min-h-1 w-4 min-w-1 rounded-full text-sm opacity-0 group-hover:opacity-100",
                classNames?.imageRemoveButton,
              )}
            >
              x
            </Button>
          </span>
        ) : (
          <div
            key={file.path}
            className={cn(
              "flex w-full items-center gap-2 rounded-lg border border-foreground-200 p-3",
              classNames?.documentItem,
            )}
          >
            <Apple className={cn("size-9", classNames?.documentIcon)} />
            <span className="flex flex-col items-start gap-0.5">
              <span
                className={cn(
                  "line-clamp-1 text-sm font-medium",
                  classNames?.name,
                )}
              >
                {file.name}
              </span>
              <span
                className={cn("text-xs text-foreground-400", classNames?.size)}
              >
                {file.size}
              </span>
            </span>

            <Button
              isIconOnly
              onPressEnd={() => removeFile(file)}
              key={file.path}
              className={cn(
                "ms-auto h-5 min-h-1 w-5 min-w-1 shrink-0 rounded-full text-sm",
                classNames?.documentRemoveButton,
              )}
            >
              x
            </Button>
          </div>
        );
      })}
    </div>
  );
};

FileList.propTypes = {
  files: PropTypes.arrayOf(PropTypes.instanceOf(File)).isRequired,
  accept: PropTypes.arrayOf(PropTypes.string),
  removeFile: PropTypes.func.isRequired,
  classNames: PropTypes.shape({
    imageItem: PropTypes.string,
    imageRemoveButton: PropTypes.string,
    documentItem: PropTypes.string,
    documentIcon: PropTypes.string,
    documentRemoveButton: PropTypes.string,
    name: PropTypes.string,
    size: PropTypes.string,
  }),
};

const getAcceptedFormats = (accept) =>
  accept
    ? accept.reduce((acc, type) => {
        const newAcc = { ...acc };
        switch (type) {
          case "image":
            newAcc["image/*"] = [".jpeg", ".png", ".webp", ".gif"];
            break;
          case "video":
            newAcc["video/*"] = [".mp4", ".mov", ".avi", ".mkv"];
            break;
          case "pdf":
            newAcc["application/pdf"] = [".pdf"];
            break;
          default:
            break;
        }
        return newAcc;
      }, {})
    : undefined;

FormUpload.propTypes = {
  name: PropTypes.string.isRequired,
  isMultiple: PropTypes.bool,
  control: PropTypes.object.isRequired,
  accept: PropTypes.arrayOf(PropTypes.string),
  label: PropTypes.string,
  classNames: PropTypes.shape({
    container: PropTypes.string,
    label: PropTypes.string,
    wrapper: PropTypes.string,
    input: PropTypes.string,
    uploadIcon: PropTypes.string,
    removeButton: PropTypes.string,
    removeAllButton: PropTypes.string,
    fileList: PropTypes.shape({
      imageItem: PropTypes.string,
      imageRemoveButton: PropTypes.string,
      documentItem: PropTypes.string,
      documentIcon: PropTypes.string,
      documentRemoveButton: PropTypes.string,
      name: PropTypes.string,
      size: PropTypes.string,
    }),
  }),
};

export default FormUpload;
